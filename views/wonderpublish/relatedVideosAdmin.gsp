<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>

<asset:stylesheet href="material-input.css"/>
<asset:stylesheet href="jquery.simple-dtpicker.css"/>
<div id ="relatedVideos" style="min-height: 400px;">
</div>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>

<script>
    function deleteVideo(videoId){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="admin" action="deleteRelatedVideo" params="'chapterId='+${chapterId}+'&videoId='+videoId" onSuccess='videoDeleted(data);'/>
    }
    function videoDeleted(data){
        alert("Video removed");
        location.reload();
    }
    displayRelatedVideos();

    function displayRelatedVideos(){
        <%if("notPresent".equals(relatedVideoStatus)){%>
        if(data.length==0){
            var cStr = "";
            cStr += "<div id='searchVideos' class='mt-4' style='width: 100%;' >";
            cStr += "<div class='d-lfex flex-wrap'>" + "<div class='dflexPr d-none d-lg-flex'>" +
                "</div>"+
                "<div style='display: flex;flex-direction: row;justify-content:center;align-items:center'>There seems to be no related videos for this chapter.<br> <br>If this is unexpected, kindly contact wonderslate. " +
                "</div>";
            $("#relatedVideos").height($(window).height());
            document.getElementById("relatedVideos").innerHTML =cStr;
            }
            <%}else {%>
            var htmlstrr = "";
            htmlstrr += "<div id='searchVideos' class='mt-4' style='width: 100%;'>";
            <%for (int i = 0; i < searchResultList.size(); ++i) {%>

            var videoId=   '\"${searchResultList[i].videoId} \"';
            htmlstrr += "<div class='video-card py-3' style='display: flex;flex-direction: row;justify-content:center;align-items:center'>" +
                "        <iframe width='620' height='315' src='https://www.youtube.com/embed/${searchResultList[i].videoId}'> </iframe> "+
                " <button class='ml-5' style='background: none;border:none' onclick='javascript:deleteVideo("+videoId+")'><img  src='${assetPath(src: 'baseline-delete-24px.svg')}' alt='wonderslate'></button>"+
                "</div>";
            <% } %>
            htmlstrr += "</div>";
            document.getElementById("relatedVideos").innerHTML = htmlstrr;
            <% }%>
    }
</script>
